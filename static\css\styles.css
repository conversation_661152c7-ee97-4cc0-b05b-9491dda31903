/* Global Styles */

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  font-size: 16px;
  line-height: 24px;
  color: var(--text);
  background-color: var(--white);
  overflow-x: hidden;
  box-sizing: border-box;
  /* Improve rendering performance */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  will-change: transform;

  cursor: url(./img/paws.svg), pointer;
}

h1,
h2,
h3,
h4,
h5,
h6,
.tl,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
.d1,
.d2,
.d3,
.d4,
.d5 {
  margin: 0;
  padding: 0;
  font-weight: var(--fw-semibold);
}

.h5 {
  --h5: 1.5em;
}



.tl {
  font-size: var(--font-tl);
  line-height: var(--line-height-tl);
}

p,
.p {
  margin: 0;
  padding: 0;
  font-size: var(--font-lg);
  line-height: var(--line-height-lg);
  font-weight: var(--fw-regular);
  color: var(--text);
  font-family: var(--font-family-sans);
}

h1,
.h1 {
  font-size: var(--font-h1);
  line-height: var(--line-height-h1);
}

h2,
.h2 {
  font-size: var(--font-h2);
  line-height: var(--line-height-h2);
}

h3,
.h3 {
  font-size: var(--font-h3);
  line-height: var(--line-height-h3);
}

h4,
.h4 {
  font-size: var(--font-h4);
  line-height: var(--line-height-h4);
}

h5,
.h5 {
  font-size: var(--font-h5);
  line-height: var(--line-height-h5);
}

h6,
.h6 {
  font-size: var(--font-h6);
  line-height: var(--line-height-h6);
}

.d1 {
  font-size: var(--font-d1);
  line-height: var(--line-height-d1);
}

.d2 {
  font-size: var(--font-d2);
  line-height: var(--line-height-d2);
}

.d3 {
  font-size: var(--font-d3);
  line-height: var(--line-height-d3);
}

.d4 {
  font-size: var(--font-d4);
  line-height: var(--line-height-d4);
}

.d5 {
  font-size: var(--font-d5);
  line-height: var(--line-height-d5);
  display: none;
}


a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--primary);
  text-decoration: underline;
}

section {
  padding: var(--spacing-6xl) 0;
}

/* Button Styles */

.primary-lg-fill {
  font-family: var(--font-family-sans);
  font-size: var(--font-lg);
  font-weight: var(--fw-medium);
  line-height: var(--line-height-lg);
  color: var(--white);
  background-color: var(--primary);
  padding: var(--spacing-r) var(--spacing-xl);
  border-radius: var(--radius-4xl);
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
  text-decoration: none;
  cursor: url(./img/paws2.svg), pointer;
}

.primary-lg-fill:hover {
  color: var(--white);
  background-color: var(--button-fill-hover);
  text-decoration: none;
}


.primary-sm-fill {
  font-family: var(--font-family-sans);
  font-size: var(--font-sm);
  font-weight: var(--fw-medium);
  line-height: var(--line-height-sm);
  color: var(--white);
  background-color: var(--primary);
  padding: var(--spacing-2md) var(--spacing-2r);
  border-radius: var(--radius-3xl);
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-sm-fill:hover {
  color: var(--white);
  background-color: var(--button-fill-hover);
  text-decoration: none;
}


.primary-lg-outline {
  font-family: var(--font-family-sans);
  font-size: var(--font-lg);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-lg);
  color: var(--primary);
  padding: calc(var(--spacing-r) - var(--border-width-2)) var(--spacing-xl);
  border-radius: var(--radius-4xl);
  border: var(--border-width-2) solid var(--primary) !important;
  border: none;
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-lg-outline:hover {
  background-color: var(--button-outline-hover) !important;
  outline: none !important;
  text-decoration: none;
  color: var(--button-text-hover) !important;
}


.primary-sm-outline {
  font-family: var(--font-family-sans);
  font-size: var(--font-sm);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-sm);
  color: var(--primary);
  padding: calc(var(--spacing-2md) - var(--border-width-2)) var(--spacing-2r);
  border-radius: var(--radius-3xl);
  border: var(--border-width-2) solid var(--primary) !important;
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-sm-outline:hover {
  background-color: var(--button-outline-hover);
  text-decoration: none;
  color: var(--button-text-hover) !important;
  text-decoration: none;
}




.secondary-lg-fill {
  font-family: var(--font-family-sans);
  font-size: var(--font-lg);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-lg);
  color: var(--white);
  background-color: var(--secondary);
  padding: var(--spacing-r) var(--spacing-xl);
  border-radius: var(--radius-4xl);
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
}

.primary-lg-fill:hover {
  background-color: var(--button-fill-hover);
}


.secondary-sm-fill {
  font-family: var(--font-family-sans);
  font-size: var(--font-sm);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-sm);
  color: var(--white);
  background-color: var(--secondary);
  padding: var(--spacing-2md) var(--spacing-2r);
  border-radius: var(--radius-3xl);
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-sm-fill:hover {
  background-color: var(--button-fill-hover);
}


.primary-lg-outline {
  font-family: var(--font-family-sans);
  font-size: var(--font-lg);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-lg);
  color: var(--primary);
  padding: calc(var(--spacing-r) - var(--border-width-2)) var(--spacing-xl);
  border-radius: var(--radius-4xl);
  border: var(--border-width-2) solid var(--primary) !important;
  border: none;
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-lg-outline:hover {
  background-color: var(--button-outline-hover) !important;
  outline: none !important;
}


.primary-sm-outline {
  font-family: var(--font-family-sans);
  font-size: var(--font-sm);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-sm);
  color: var(--primary);
  padding: calc(var(--spacing-2md) - var(--border-width-2)) var(--spacing-2r);
  border-radius: var(--radius-3xl);
  border: var(--border-width-2) solid var(--primary) !important;
  border: none;
  cursor: pointer;
  transition: var(--transition-slow);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.primary-sm-outline:hover {
  background-color: var(--button-outline-hover);
}




























/* .container {
  max-width: var(--breakpoint-xl);
  margin: 0 auto;
  padding: var(--spacing-r) var(--spacing-lg);
} */

nav {
  padding: var(--spacing-r) var(--spacing-lg);
  align-items: center;
  display: flex;
  justify-content: space-between;
  background-color: var(--white);
  /* position: sticky; */
  top: 0;
  z-index: 999;

}

.logo-container a {
  font-family: var(--font-family-sans);
  font-size: var(--font-tl);
  line-height: var(--line-height-tl);
  font-weight: var(--fw-semibold);
  color: var(--primary);
  text-decoration: none;
  display: flex;
  cursor: pointer;
}

.links {
  display: none;
}

.hero {
  padding: var(--spacing-5xl) 0;
  height: 80vh;
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  max-width: 80%;
  margin: 0 auto;
  padding-bottom: 380px;
}

.hero-text-container h1 {
  margin-bottom: var(--spacing-lg);
}

.hero-text-container p {
  margin-bottom: var(--spacing-lg);
}


.hero img {
  position: absolute;
}

.hero img:nth-child(2) {
  width: 380px;
  bottom: 0%;
  left: 62%;
  transform: translateX(-50%);
  z-index: -1;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
}

.hero img:nth-child(3) {
  display: block;
  width: 72px;
  top: 0%;
  left: 0%;
  rotate: -30deg;
  z-index: -1;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.hero img:nth-child(4) {
  display: block;
  width: 300px;
  bottom: 3%;
  left: 29%;
  transform: translateX(-50%);
  z-index: -1;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
}

.home-list-section {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
  will-change: transform;
  width: 100%;
  /* overflow: hidden; */
}

.home-list-section .home-list-item-container {
  width: 100%;
  position: relative;
  margin: 0 auto;
}


/* Adjust for the slide margins */
.home-list-section .slick-list {
  margin: 0 -12px;
  /* Negative margin to counteract the slide margins */
}

/* Slick specific styles */
.slick-track {
  display: flex;
  align-items: center;
}

/* Add 24px gap between slides */
.slick-slide {
  margin: 0 12px;
  /* 24px total gap (12px on each side) */
  transition: transform 0.3s ease;
}

.slick-center {
  z-index: 10;
  box-shadow: var(--shadow-xl);
}

.slick-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  transition: background-color 0.3s ease;
  z-index: 10;
  margin: 0 var(--spacing-sm);
}

.slick-arrow:hover {
  background-color: var(--primary-700);
}

.slick-arrow:active {
  transform: scale(0.95);
}

.slick-arrow img {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}

.slick-list {
  overflow: visible;
}

.home-list-section .home-list-item-container .home-list-item {
  min-width: 300px;
  min-height: 480px;
  position: relative;
  border-radius: var(--radius-6xl);
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.6s cubic-bezier(0.25, 1, 0.5, 1);
  transform-origin: center center;
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform-style: preserve-3d;
}


.home-list-section .action-container .icons-container {
  display: none;
}

.home-list-section .home-list-item-container .home-list-item:nth-child(3n+1) {
  background-color: var(--primary-300);
}

.home-list-section .home-list-item-container .home-list-item:nth-child(3n+2) {
  background-color: var(--secondary-300);
}

.home-list-section .home-list-item-container .home-list-item:nth-child(3n+3) {
  background-color: var(--tertiary-300);
}

/* Apply the same colors to cloned items */
.home-list-section .home-list-item-container .home-list-item.clone:nth-child(3n+1) {
  background-color: var(--primary-300);
}

.home-list-section .home-list-item-container .home-list-item.clone:nth-child(3n+2) {
  background-color: var(--secondary-300);
}

.home-list-section .home-list-item-container .home-list-item.clone:nth-child(3n+3) {
  background-color: var(--tertiary-300);
}





.home-list-section .home-list-item-container .home-list-item img {
  width: 80%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.home-list-section .action-container {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-top: var(--spacing-lg);
  gap: var(--spacing-lg);
}


.home-list-section .action-container .icons-container .icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease-in-out, background-color 0.3s ease;
}

.home-list-section .action-container .icons-container .icon:hover {
  background-color: var(--primary-700);
}

.home-list-section .action-container .icons-container .icon:active,
.home-list-section .action-container .icons-container .icon.clicked {
  transform: scale(0.95);
  background-color: var(--primary-800);
}

.home-list-section .action-container .icons-container .icon img {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}

.home-list-section .home-list-item-container .home-list-item.active {
  z-index: 10;
  box-shadow: var(--shadow-xl);
}






@media (min-width: 320px) {
  .hero .hero-heading {
    display: block;
    font-size: 2em;
  }
}

@media (min-width: 576px) {
  .hero .hero-heading {
    font-size: 2.5em;
  }
}

@media (min-width: 768px) {
  .feature-list-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .detail-cards-container {
    flex-wrap: nowrap;
    gap: var(--spacing-2xl);
  }

  .detail-text-container {
    padding: var(--spacing-4xl);
  }

  .detail-text-container h3 {
    font-size: var(--font-h4);
  }

  .detail-card {
    min-width: 200px;
  }

  .hero .hero-heading {
    font-size: 3em;
  }
}

@media (min-width: 992px) {

  .container {
    padding: var(--spacing-r) var(--spacing-3xl);
  }


  .hero img:nth-child(2) {
    width: 338px;
    bottom: -2%;
    left: 71%;
    transform: none;
    z-index: -1;
  }


  .hero img:nth-child(3) {
    display: block;
    width: 123px;
    top: 5%;
    left: 3%;
    rotate: -14deg;
    z-index: -1;
  }

  .hero img:nth-child(4) {
    display: block;
    width: 282px;
    bottom: 0%;
    left: 15%;
    transform: translateX(-50%);
    z-index: -1;
  }

  .hero-text-container {
    padding-bottom: 120px;
  }

  .home-list-section .home-list-item-container .home-list-item {
    min-width: 420px;
    min-height: 656px;
  }

  .feature-list-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .feature-section {
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
    background-color: var(--primary-50);
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    padding-top: var(--spacing-3xl);
    padding-bottom: var(--spacing-3xl);
    position: relative;
  }

  .feature-list-container {
    padding: 0 var(--spacing-3xl) !important;
  }
  .details-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-3xl);
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
  }

  .detail-cards-container {
    order: 2;
    width: 45%;
    margin-bottom: 0;
  }

  .detail-text-container {
    order: 1;
    width: 55%;
    text-align: left;
  }

  .hero .hero-heading {
    font-size: 3.5em;
  }
}




@media (min-width: 1200px) {

  body {
    font-size: 18px;
  }

  .container {
    padding: var(--spacing-r) var(--spacing-5xl);
  }

  .links {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
  }

  .hero-text-container h1 {
    margin-bottom: var(--spacing-2xl);
  }

  .hero .hero-heading {
    font-size: 4em;
  }


  .hero img:nth-child(3) {
    display: block;
    width: 120px;
    top: 4%;
    left: 2%;
    rotate: -30deg;
    z-index: -1;
  }

  .hero img:nth-child(4) {
    display: block;
    width: 278px;
    bottom: -2%;
    left: 9%;
    transform: translateX(-50%);
    z-index: -1;
  }



  .home-list-section .action-container .icons-container {
    position: absolute;
    right: 0;
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
  }

  .home-list-section .action-container .icons-container .icon {
    background-color: var(--primary-50);
    padding: var(--spacing-lg);
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .home-list-section .action-container .icons-container .icon:hover {
    background-color: var(--primary-100);
    scale: 1.1;
  }

  .home-list-section .action-container .icons-container .icon img {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(15%) sepia(80%) saturate(4000%) hue-rotate(214deg) brightness(94%) contrast(101%);
  }

  .feature-list-container {
    padding: 0 var(--spacing-5xl) !important;
  }







}

@media (min-width: 1400px) {

  body {
    font-size: 22px;
  }

  .detail-card>img:first-child {
    width: 150px;
    height: 150px;
  }

  .detail-text-container h3 {
    font-size: var(--font-h2);
  }

  .hero .hero-heading {
    font-size: 4.5em;
  }

  .hero img:nth-child(2) {
    width: 407px;
    bottom: 1%;
    left: 76%;
    transform: none;
    z-index: -1;
  }

  .hero img:nth-child(3) {
    display: block;
    width: 136px;
    top: 4%;
    left: 1%;
    rotate: -30deg;
    z-index: -1;
  }

  .hero img:nth-child(4) {
    display: block;
    width: 298px;
    bottom: 4%;
    left: 10%;
    transform: translateX(-50%);
    z-index: -1;
  }

  .hero-text-container p {
    margin-bottom: var(--spacing-3xl);
  }

  .home-list-section .home-list-item-container .home-list-item {
    min-width: 480px;
    min-height: 720px;
  }

  .home-list-section {
    gap: var(--spacing-4xl);
  }


}

@media (min-width: 1600px) {}

@media (min-width: 1800px) {}

/* Feature Section Styles */

.feature-section {
  padding: var(--spacing-5xl) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
  margin: 0 auto;
  background-color: var(--primary-50);
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  position: relative;
}

.feature-text-container {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.feature-text-container h2 {
  margin-bottom: var(--spacing-lg);
}


.feature-list-container {
  display: grid;
  gap: var(--spacing-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.feature-list-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-xl);
  border-radius: var(--radius-4xl);
  background-color: var(--white);
  transition: var(--transition-base);
}

.feature-section .feature-list-item h5 {
  margin-bottom: var(--spacing-lg);
}



.feature-list-item img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--radius-circle);
  margin-bottom: var(--spacing-lg);
  border: 4px solid var(--primary-100);
}


.feature-list-item p {
  font-size: var(--font-sm);
  line-height: var(--line-height-sm);
}

/* Details Section Styles */

.details-section {
  padding: var(--spacing-5xl) 0;
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.details-section::before {
  content: '';
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  border-radius: var(--radius-circle);
  background-color: var(--primary-50);
  opacity: 0.4;
  z-index: 0;
}

.details-section::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 250px;
  height: 250px;
  border-radius: var(--radius-circle);
  background-color: var(--secondary-50);
  opacity: 0.4;
  z-index: 0;
}

.details-section>* {
  position: relative;
  z-index: 1;
}

.detail-cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 var(--spacing-lg);
}

/* Mobile styles for details section */
@media (max-width: 767px) {
  .detail-cards-container {
    flex-direction: column;
    align-items: center;
  }

  .detail-card {
    max-width: 100%;
    width: 100%;
    margin-bottom: var(--spacing-xl);
  }

  .detail-text-container {
    padding: var(--spacing-2xl);
  }

  .detail-text-container a {
    display: block;
    margin: var(--spacing-lg) auto;
    max-width: 200px;
  }
}

.detail-card {
  flex: 1;
  min-width: 250px;
  max-width: 300px;
  background-color: var(--white);
  border-radius: var(--radius-6xl);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-2xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.detail-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-100);
}

.detail-card>img:first-child {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: var(--radius-circle);
  margin-bottom: var(--spacing-xl);
  border: 4px solid var(--primary-100);
  transition: var(--transition-base);
  background-color: var(--gray-100);
}

.detail-card:hover>img:first-child {
  border-color: var(--primary);
  transform: scale(1.05);
}

.detail-card p {
  font-weight: var(--fw-semibold);
  color: var(--primary);
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-tl);
}

.detail-card a {
  position: absolute;
  bottom: var(--spacing-xl);
  right: var(--spacing-xl);
  background-color: var(--primary-50);
  border-radius: var(--radius-circle);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.detail-card a:hover {
  background-color: var(--primary);
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.detail-card a img {
  width: 24px;
  height: 24px;
  filter: brightness(0) saturate(100%) invert(22%) sepia(88%) saturate(2538%) hue-rotate(211deg) brightness(94%) contrast(98%);
  transition: var(--transition-base);
}

.detail-card a:hover img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
}

.detail-text-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-4xl);
  background-color: var(--white);
  border-radius: var(--radius-6xl);
  box-shadow: var(--shadow-lg);
  text-align: center;
  position: relative;
  z-index: 2;
  border: 1px solid var(--gray-200);
}

.detail-text-container h3 {
  color: var(--primary);
  margin-bottom: var(--spacing-xl);
  font-weight: var(--fw-bold);
}

.detail-text-container p {
  color: var(--gray-700);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.6;
}

.detail-text-container a {
  margin: 0 var(--spacing-lg);
  display: inline-block;
}

.detail-text-container a.primary-sm-fill,
.detail-text-container a.primary-sm-outline {
  min-width: 120px;
  text-align: center;
}

.detail-text-container a.primary-sm-outline {
  margin-top: var(--spacing-md);
}