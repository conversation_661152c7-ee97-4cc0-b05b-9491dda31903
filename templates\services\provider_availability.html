{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Manage Availability - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.service_provider.profile_picture %}
                        <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.full_name|default:user.email }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="/static/img/default-profile.png" alt="{{ user.full_name|default:user.email }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                    <h5 class="mb-0">{{ user.full_name|default:user.email }}</h5>
                    <p class="text-muted">Service Provider</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">View Public Profile</a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'provider-dashboard' %}" class="list-group-item list-group-item-action {% if active_tab == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'provider-services' %}" class="list-group-item list-group-item-action {% if active_tab == 'services' %}active{% endif %}">
                        <i class="fas fa-concierge-bell me-2"></i> My Services
                    </a>
                    <a href="{% url 'provider-bookings' %}" class="list-group-item list-group-item-action {% if active_tab == 'bookings' %}active{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a href="{% url 'provider-availability' %}" class="list-group-item list-group-item-action {% if active_tab == 'availability' %}active{% endif %}">
                        <i class="fas fa-clock me-2"></i> Availability
                    </a>
                    <a href="{% url 'provider-settings' %}" class="list-group-item list-group-item-action {% if active_tab == 'settings' %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Availability Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Manage Availability</h5>
                    <button type="button" class="btn btn-primary" onclick="openAvailabilityModal()">
                        <i class="fas fa-plus-circle"></i> Add Availability
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Day</th>
                                            <th>Morning (6AM-12PM)</th>
                                            <th>Afternoon (12PM-5PM)</th>
                                            <th>Evening (5PM-10PM)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for day_name, day_slots in availability_by_day.items %}
                                        <tr>
                                            <td class="fw-bold">{{ day_name }}</td>
                                            <td>
                                                {% for slot in day_slots.morning %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.afternoon %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.evening %}
                                                <div class="availability-slot">
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="slot-delete-form">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn-delete" onclick="return confirm('Are you sure you want to delete this availability slot?')" title="Delete availability">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Availability Tips -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Availability Tips</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Set your availability for each day of the week to let clients know when you can provide services.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    You can add multiple time slots for each day to accommodate different schedules.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Clients can only book appointments during your available time slots.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Remember to keep your availability up to date to avoid scheduling conflicts.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Availability Modal -->
<div id="availabilityModal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h3>Add Availability</h3>
            <button type="button" class="modal-close" onclick="closeAvailabilityModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form method="post">
            {% csrf_token %}
            <div class="modal-body">
                <div class="form-group">
                    <label for="{{ availability_form.day_of_week.id_for_label }}">Day of Week:</label>
                    {{ availability_form.day_of_week }}
                </div>
                <div class="form-group">
                    <label for="{{ availability_form.start_time.id_for_label }}">Start Time:</label>
                    {{ availability_form.start_time }}
                </div>
                <div class="form-group">
                    <label for="{{ availability_form.end_time.id_for_label }}">End Time:</label>
                    {{ availability_form.end_time }}
                </div>
                {% if availability_form.errors %}
                    <div class="form-errors">
                        {{ availability_form.errors }}
                    </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAvailabilityModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Add Availability</button>
            </div>
        </form>
    </div>
</div>

<script>
function openAvailabilityModal() {
    document.getElementById('availabilityModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeAvailabilityModal() {
    document.getElementById('availabilityModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('availabilityModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAvailabilityModal();
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAvailabilityModal();
    }
});
</script>

<style>
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-container {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-2xl);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-800);
    font-size: var(--font-xl);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: var(--font-lg);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-base);
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

.modal-body {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-base);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: var(--fw-medium);
    color: var(--gray-700);
}

.form-group select,
.form-group input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    transition: var(--transition-base);
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-errors {
    background-color: var(--danger-light);
    border: 1px solid var(--danger);
    color: var(--danger-dark);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    margin-top: var(--spacing-sm);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-base);
    border-radius: var(--radius-md);
    font-size: var(--font-sm);
    font-weight: var(--fw-medium);
    cursor: pointer;
    transition: var(--transition-base);
    border: 2px solid transparent;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-200);
    border-color: var(--gray-400);
}

/* Availability Slots Styling */
.availability-slot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-sm);
    background-color: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    transition: var(--transition-base);
}

.availability-slot:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
}

.slot-time {
    font-size: var(--font-sm);
    color: var(--gray-700);
    font-weight: var(--fw-medium);
}

.slot-delete-form {
    display: inline;
}

.btn-delete {
    background: none;
    border: none;
    color: var(--danger);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-base);
    font-size: var(--font-xs);
}

.btn-delete:hover {
    background-color: var(--danger-light);
    color: var(--danger-dark);
}

.text-muted {
    color: var(--gray-500) !important;
    font-size: var(--font-sm);
    font-style: italic;
}

/* Table styling */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
}

.table th,
.table td {
    padding: var(--spacing-base);
    border: 1px solid var(--gray-200);
    text-align: left;
    vertical-align: top;
}

.table th {
    background-color: var(--gray-50);
    font-weight: var(--fw-semibold);
    color: var(--gray-800);
    font-size: var(--font-sm);
}

.table td {
    background-color: var(--white);
}

.fw-bold {
    font-weight: var(--fw-bold);
}

/* Card styling */
.card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-lg);
}

.card-header {
    padding: var(--spacing-base) var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
    background-color: var(--gray-50);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-body {
    padding: var(--spacing-lg);
}

.card h5 {
    margin: 0;
    font-size: var(--font-lg);
    font-weight: var(--fw-semibold);
    color: var(--gray-800);
}

/* List styling */
.list-group {
    list-style: none;
    padding: 0;
    margin: 0;
}

.list-group-item {
    padding: var(--spacing-base);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    color: var(--gray-700);
    font-size: var(--font-sm);
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item i {
    margin-right: var(--spacing-sm);
    color: var(--primary);
}
</style>
{% endblock %}
